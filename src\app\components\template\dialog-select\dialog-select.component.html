<div fxLayout="row" fxLayoutWrap="wrap">
	<div fxFlex="100">
        <h2 mat-dialog-title>{{ data.title || 'Selecione itens' }}</h2>
        <mat-dialog-content>
            <mat-selection-list [(ngModel)]="selected">
                <mat-list-option *ngFor="let item of data.items" [value]="item" style="margin: 5px;">
                {{ item[data.displayField || 'nome'] }}
                </mat-list-option>
            </mat-selection-list>
        </mat-dialog-content>
        <mat-dialog-actions align="end">
            <button mat-button mat-dialog-close>Cancelar</button>
            <button mat-button [mat-dialog-close]="selected">Confirmar</button>
            <button mat-button color="primary" [mat-dialog-close]="true" cdkFocusInitial>Criar um novo</button>
        </mat-dialog-actions>
	</div>
</div>