import { Component, OnInit, Inject } from '@angular/core';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';

@Component({
  selector: 'app-dialog-select',
  templateUrl: './dialog-select.component.html',
  styleUrls: ['./dialog-select.component.css']
})
export class DialogSelectComponent implements OnInit {
  selected: any[] = [];
  createNew: boolean = false;

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: {
      items: any[],
      createNew?: boolean,
      title?: string,
      displayField?: string
    }
  ) {}

  ngOnInit(): void {
  }

}
